import { useEffect, useRef, useState } from "react";
import { MinSearchDataLen, options, units } from "../../../common";
import { useDebouncedValue } from "@mantine/hooks";
import axios from "axios";
import { getValidSearchData, searchProducts, useBuyerSettingStore, useGlobalStore, useSearchStore } from '@bryzos/giss-ui-library';
import styles from './productSearch.module.scss';
import { ReactComponent as SearchIcon } from '../../../assets/New-images/Search.svg';
import { ReactComponent as DropdownArrow } from '../../../assets/New-images/New-Image-latest/Polygon.svg';
import clsx from "clsx";
import { MenuItem, Select } from "@mui/material";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { formatOrderSizeToDisplayText, updateSelectedPriceSearchData } from "src/renderer2/helper";
dayjs.extend(customParseFormat);

type ProductSearchState = {
};

const ProductSearch: React.FC<ProductSearchState> = ({}) => {
    const { shortListedSearchProductsData, searchByProductResult, setSearchByProductResult, searchString, setSearchString, searchZipCode, setSearchZipCode, sessionId, enableRejectSearchAnalytic, setEnableRejectSearchAnalytic } = useSearchStore();
    const productData: any = useGlobalStore(state => state.productData);
    const referenceData: any = useGlobalStore(state => state.referenceData);
    const setOrderSizeSliderValue = useSearchStore(state => state.setOrderSizeSliderValue);
    const orderSizeSliderValue = useSearchStore(state => state.orderSizeSliderValue);
    const selectedDomesticOption = useSearchStore(state => state.selectedDomesticOption);
    const setSelectedDomesticOption = useSearchStore(state => state.setSelectedDomesticOption);
    const selectedPriceUnit = useSearchStore(state => state.selectedPriceUnit);
    const setSelectedPriceUnit = useSearchStore(state => state.setSelectedPriceUnit);
    const selectedSavedSearch = useSearchStore(state => state.selectedSavedSearch);
    const [debouncedSearchData] = useDebouncedValue(searchString, 400);
    const [placeholder, setPlaceholder] = useState('Search by Product');
    const [orderSizeList, setOrderSizeList] = useState<any[]>([]);
    const searchProductInputRef = useRef<any>(null);
    const {buyerSetting} = useBuyerSettingStore();

useEffect(()=>{
    if(referenceData?.ref_weight_price_brackets?.length > 0){
        setOrderSizeList(referenceData.ref_weight_price_brackets)
    }
},[referenceData])
    useEffect(()=>{
        if(buyerSetting?.price_search_zip){
            setSearchZipCode(buyerSetting.price_search_zip);
        }
    },[buyerSetting])

    const initialPlaceholder = 'Search by Product';
    const secondPlaceholder = 'Search by Product';
    const focusPlaceholder = 'Example: Beam 12" x...';

    useEffect(()=>{
        if(searchByProductResult.length === 0 && searchProductInputRef.current){
            searchProductInputRef.current.focus();
        }
    },[searchByProductResult])

    const handleFocus = () => {
        setPlaceholder(focusPlaceholder);
    };

    const handleBlur = () => {
        setPlaceholder(shortListedSearchProductsData.length === 0 ? initialPlaceholder : secondPlaceholder);
    };

    useEffect(() => {
        if (debouncedSearchData?.length >= MinSearchDataLen) {
            searchAnalyticsApi(sessionId, null, debouncedSearchData)
        }
    }, [debouncedSearchData])

    useEffect(() => {
        if (sessionId) {
            if (searchString.length === 1) {
                setEnableRejectSearchAnalytic(true)
            }
            if (searchString.length === 0 && searchByProductResult.length === 0 && debouncedSearchData && enableRejectSearchAnalytic) {
                searchAnalyticsApi(sessionId, 'Reject', debouncedSearchData)
            } else if (debouncedSearchData) {
                searchAnalyticsApi(sessionId, 'Accept', debouncedSearchData)
            }
        }
    }, [searchString])

    const searchHandler = (text: string) => {
        setSearchString(text);
        handleProductSearch(text);
    }

    const handleProductSearch = (searchString: string) => {
        const searchStrings = getValidSearchData(searchString);
        const productSearch = searchProducts(productData, searchStrings, searchString, undefined, false);
        const filterProductSearch = productSearch.filter((product) => {
            if (shortListedSearchProductsData.length !== 0 && shortListedSearchProductsData.some((data) => data.id === product.Product_ID)) {
                return false;
            } else {
                return true;
            }
        })
        setSearchByProductResult(filterProductSearch);

    }

    const searchAnalyticsApi = (sessionId: string, status: string | null, searchKeyword: string) => {
        const payload = {
            "data": {
                "session_id": sessionId,
                "keyword": searchKeyword,
                "status": status,
                "source": 'search'
            }
        }
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/create_po_search', payload)
            .catch(err => console.error(err))
    }

    return (
        <>
        {selectedSavedSearch?.pricing_expired ? (
            <>
                <div>
                    <span>Price List Name: {selectedSavedSearch.title}</span>
                    <span>Order Size: {selectedSavedSearch?.order_size ? formatOrderSizeToDisplayText(orderSizeList, Number(selectedSavedSearch?.order_size)) : '-'}</span>
                    <span>Destination Zip Code: {selectedSavedSearch.zipcode}</span>
                </div>
                <div>
                    <span>EXPIRED PRICING</span>
                    <span>LIST CREATE DATE:</span>
                    <span>{dayjs(selectedSavedSearch.search_date_time, "M/D/YY [@] hh:mma [CT]").format("ddd, MMM DD, YYYY hh:mmA [CT]")}</span>
                    
                </div>
            </>
        ) : (
            <>
                <div className={styles.searchFilterSection}>
                    <div
                        className={styles.zipBox}
                        data-hover-video-id='search-zip'
                    >
                        <span className={styles.destZIP}>Dest. Zip Code</span>
                        <span className={styles.zipCode}>
                            <input
                                type="text"
                                value={searchZipCode}
                                onChange={(e) => {
                                    e.target.value = e.target.value.replace(/\D/g, '');
                                    setSearchZipCode(e.target.value)
                                }}
                                maxLength={5}
                                className={styles.zipInput}
                            />
                        </span>
                    </div>
                    <div className={styles.dropdownBox}>
                        <span className={styles.dropdownLabel}>Order Size (LB)</span>
                        <Select
                            value={orderSizeSliderValue}
                            onChange={(event: any) => {
                                setOrderSizeSliderValue(Number(event.target.value))
                                setTimeout(()=>{
                                    updateSelectedPriceSearchData(shortListedSearchProductsData);
                                }, 0)
                            }}
                            className={clsx('productSearchDropdown', styles.dropdownValue)}
                            IconComponent={DropdownArrow}
                            MenuProps={
                                {
                                    classes: {
                                        paper: styles.dropDownBG
                                    },
                                }
                            }
                        >   
                        
                            {orderSizeList.map((item: any) => (
                                <MenuItem key={item.min_weight} value={Number(item.min_weight)}>
                                    {Number(item.min_weight) === Number(orderSizeList[orderSizeList.length - 1].min_weight) ? Number(item.min_weight).toLocaleString() + '+' : `${Number(item.min_weight).toLocaleString()} to ${Number(item.max_weight).toLocaleString()}`}
                                </MenuItem>
                            ))}
                        </Select>
                    </div>
                    <div className={styles.dropdownBox}>
                        <span className={styles.dropdownLabel}>Pricing Units</span>
                        <Select
                            value={selectedPriceUnit}
                            onChange={(event: any) => {
                                setSelectedPriceUnit(event.target.value as string)
                            }}
                            className={clsx('productSearchDropdown', styles.dropdownValue)}
                            IconComponent={DropdownArrow}
                            MenuProps={
                                {
                                    classes: {
                                        paper: styles.dropDownBG
                                    },
                                }
                            }
                        >
                            {units.map((item: any) => (
                                <MenuItem key={item.value} value={item.value}>
                                    {item.title}
                                </MenuItem>
                            ))}
                        </Select>
                    </div>
                    <div className={styles.dropdownBox}>
                        <span className={styles.dropdownLabel}>Domestic Required</span>
                        <Select
                            value={selectedDomesticOption}
                            onChange={(event: any) => {
                                setSelectedDomesticOption(event.target.value as boolean)
                            }}
                            className={clsx('productSearchDropdown', styles.dropdownValue)}
                            IconComponent={DropdownArrow}
                            MenuProps={
                                {
                                    classes: {
                                        paper: styles.dropDownBG
                                    },
                                }
                            }
                        >
                            {options.map((item: any) => (
                                <MenuItem key={item.value} value={item.value}>
                                    {item.title}
                                </MenuItem>
                            ))}
                        </Select>
                    </div>
                </div>
                <div className={styles.searchSection}>
                    <div className={clsx(searchString.length > 0 ? styles.searchInputStartTyping : "", styles.searchBox)}
                        data-hover-video-id='price-search'
                    >
                        <SearchIcon />
                        <input placeholder={placeholder} onFocus={handleFocus} onBlur={handleBlur} value={searchString} onChange={event => { searchHandler(event.target.value) }} ref={searchProductInputRef} />
                    </div>
                </div>
            </>
        )}

        </>
    )
}

export default ProductSearch;