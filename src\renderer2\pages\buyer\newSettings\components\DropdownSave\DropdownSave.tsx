import React, { useState, useRef } from 'react';
import { ClickAwayListener } from '@mui/material';
import styles from './Dropdown.module.scss';
import { ReactComponent as DropdownIcon } from '../../../../../assets/New-images/StateIconDropDpown.svg';
import clsx from 'clsx';
import { navigatePage } from 'src/renderer2/helper';
import { routes } from 'src/renderer2/common';

interface DropdownSaveButtonProps {
    onSave: () => void;
    onSaveAndNext?: () => void;
    onSaveAndExit?: () => void;
    isDisabled?: boolean;
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    className?: string;
    buttonText?: string;
    dropdownOptions?: {
        save?: string;
        saveAndNext?: string;
        saveAndExit?: string;
    };
}

const DropdownSave: React.FC<DropdownSaveButtonProps> = ({
    onSave,
    onSaveAndNext,
    onSaveAndExit,
    isDisabled = false,
    position = 'top-right',
    className = '',
    buttonText = 'Save',
    dropdownOptions = {
        save: 'Save',
        saveAndNext: 'Save & Next',
        saveAndExit: 'Save & Exit'
    }
}) => {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    const handleDropdownToggle = () => {
        if (isDisabled) return;
        setIsDropdownOpen(!isDropdownOpen);
    };

    const handleClickAway = () => {
        setIsDropdownOpen(false);
    };

    const getPositionStyles = (): React.CSSProperties => {
        switch (position) {
            case 'top-left':
                return { position: 'absolute', top: 0, left: 0 };
            case 'bottom-right':
                return { position: 'absolute', bottom: 0, right: 0 };
            case 'bottom-left':
                return { position: 'absolute', bottom: 0, left: 0 };
            default:
                return { position: 'absolute', top: 0, left: 0 };
        }
    };

    const handleSaveAndExit = () => {
        onSave();
        navigatePage(location.pathname, { path: routes.homePage })
    }
    console.log('dorpd own save button')
    return (
        <ClickAwayListener onClickAway={handleClickAway}>
            <div ref={dropdownRef} style={getPositionStyles()} className={className}>
                <div className={styles.buttonContainer}>
                    <button 
                        disabled={isDisabled} 
                        onClick={onSave}
                        className={styles.saveButton}
                    >
                        {buttonText}
                    </button>
                    <div className={styles.separator} />
                    <button
                        disabled={isDisabled}
                        onClick={handleDropdownToggle}
                        className={clsx(styles.dropdownButton, isDropdownOpen && styles.dropdownButtonActive)}
                    >
                        <DropdownIcon/>
                    </button>
                </div>
                
                {isDropdownOpen && !isDisabled && (
                    <div className={styles.dropdownMenu}>
                        <button
                            onClick={onSave}
                            className={styles.dropdownItem}
                        >
                            {dropdownOptions.save}
                        </button>
                        {onSaveAndNext && (
                            <button
                                onClick={onSaveAndNext}
                                className={styles.dropdownItem}
                            >
                                {dropdownOptions.saveAndNext}
                            </button>
                        )}
                            <button
                                onClick={handleSaveAndExit}
                                className={styles.dropdownItem}
                            >
                                {dropdownOptions.saveAndExit}
                            </button>
                    </div>
                )}
            </div>
        </ClickAwayListener>
    );
};

export default DropdownSave;
