import React, { useEffect, useRef, useState } from 'react';
import styles from './ShipmentTab.module.scss';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, Controller } from 'react-hook-form';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import CustomToggleCheckbox from 'src/renderer2/component/CustomToggleCheckbox';
import clsx from 'clsx';
import { shipmentsSchema } from '../schemas';
import { BReceivingHoursTooltip, deleteCertificateLineTooltip, deleteCertificateTooltip } from 'src/renderer2/tooltip';
import { Dialog, Fade, Tooltip, Autocomplete, TextField, Select, MenuItem } from '@mui/material';
import { CustomMenu } from '../../CustomMenu';
import axios from 'axios';
import { buyerSettingConst, formatPhoneNumber, formatPhoneNumberRemovingCountryCode, useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { commomKeys, defaultResaleCertificateLine, prefixUrl, reactQueryKeys, RecevingHoursFrom, RecevingHoursTo } from 'src/renderer2/common';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import ResaleCertificateLineComponent from '../components/ResaleCertificateLineComponent';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/New-Image-latest/shipment-popup-close.svg';
import { v4 as uuidv4 } from 'uuid';
import { useQueryClient } from '@tanstack/react-query';
import { unformatPhoneNumber } from 'src/renderer2/helper';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import SingleStateSelector from '../components/StateSelector/SingleStateSelector';
import { EmailTagInputField } from 'src/renderer2/component/EmailTagInput';

interface InputFocusState {
  locationNickName: boolean;
  locationAddressLine1: boolean;
  locationAddressLine2: boolean;
  locationAddressCity: boolean;
  locationAddressState: boolean;
  locationAddressZip: boolean;
  deliveryContactFirstName: boolean;
  deliveryContactLastName: boolean;
  deliveryContactPhoneNumber: boolean;
  deliveryContactEmail: boolean;
  shippingDocsEmail: boolean;
}

const MenuPropsTop = {
  classes: {
    paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown, styles.receivingHoursTop),
    list: styles.muiMenuList
  },
  anchorOrigin: {
    vertical: -5,
    horizontal: "left"
  },
  transformOrigin: {
    vertical: "bottom",
    horizontal: "left"
  },
}
const MenuPropsBottom = {
  classes: {
    paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown,styles.receivingHoursBottom),
    list: styles.muiMenuList
  },
  anchorOrigin: {
    vertical: 27,
    horizontal: "left"
  },
  transformOrigin: {
    vertical: "top",
    horizontal: "left"
  },
}

const ShipmentsTab = ({selectedShipment, isCreate, closeDialog}: any) => {
  const { userData, showLoader, setShowLoader, referenceData }: any = useGlobalStore();
  const [States, setStates] = useState([]);
  const [ResaleExpiration, setResaleExpiration] = useState([]);
  const { showCommonDialog, resetDialogStore }: any = useDialogStore();
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [payloadData, setPayloadData] = useState('');
  const queryClient = useQueryClient();
  const shipmentPopupRef = useRef(null);
  const { mutateAsync: saveUserSettings } = useSaveUserSettings();
  const { buyerSetting }: any = useBuyerSettingStore();
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
    getFieldState,
  } = useForm({
    resolver: yupResolver(shipmentsSchema),
    mode: 'onBlur',
  });
  const isButtonDisabled = !isValid || !isDirty || isSubmitting;

  const defaultUserAvailability = [
    {
      "day": "Monday",
      "from": "7",
      "to": "16",
      "display_name": "Mon",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Tuesday",
      "from": "7",
      "to": "16",
      "display_name": "Tue",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Wednesday",
      "from": "7",
      "to": "16",
      "display_name": "Wed",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Thursday",
      "from": "7",
      "to": "16",
      "display_name": "Thu",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Friday",
      "from": "7",
      "to": "16",
      "display_name": "Fri",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    }
  ]

  useEffect(() => {
    if (isCreate) {
      setValue("dates", defaultUserAvailability)
    }else{
      setValue("id", selectedShipment?.id);
      setValue("locationNickName", selectedShipment?.nickname);
      setValue('locationAddress', {
        line1: selectedShipment?.address?.line1 || '',
        line2: selectedShipment?.address?.line2 || '',
        city: selectedShipment?.address?.city || '',
        state: selectedShipment?.address?.state_id || '',
        stateCode: selectedShipment?.address?.state_code || '',
        zip: selectedShipment?.address?.zip || '',
      });
      setValue("deliveryApptRequired", selectedShipment?.appointmentRequired);
      setValue("deliveryContact", {
        firstName: selectedShipment?.deliveryContact?.firstName,
        lastName: selectedShipment?.deliveryContact?.lastName,
        phone: selectedShipment?.deliveryContact?.phone
        ? formatPhoneNumber(
            formatPhoneNumberRemovingCountryCode(selectedShipment?.deliveryContact?.phone)
          )
        : '',
        email: selectedShipment?.deliveryContact?.email,
      });
      setValue("shippingDocsEmail", selectedShipment?.shippingDocsEmail);
      if (selectedShipment?.user_delivery_receiving_availability_details) {
        const weeks =
          selectedShipment?.user_delivery_receiving_availability_details.map(
            (day: any) => {
              day.receivingHrsFrom = [...RecevingHoursFrom];
              day.receivingHrsTo = [...RecevingHoursTo];
              return day;
            }
          );
        setValue('dates', weeks);
      }
    }
  }, [isCreate, buyerSetting , selectedShipment]);

  
  useEffect(() => {
    if (referenceData) {
      setStates(referenceData.ref_states);
      // let expiresData = [];
      // referenceData?.ref_resale_cert_expiration.map((expiration) => {
      //   const expireData = {
      //     title: expiration.expiration_display_string,
      //     value: expiration.expiration_value
      //   }
      //   return expiresData = [...expiresData, expireData];
      // })


      // setResaleExpiration(expiresData);
    }
  }, [referenceData]);

  useEffect(() => {
    handleStateZipValidation("locationAddress.zip", "locationAddress.state");
  }, [watch('locationAddress.zip'), watch('locationAddress.state')])

  const deliveryApptRequired = watch('deliveryApptRequired');

  const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
    locationNickName: false,
    locationAddressLine1: false,
    locationAddressLine2: false,
    locationAddressCity: false,
    locationAddressState: false,
    locationAddressZip: false,
    deliveryContactFirstName: false,
    deliveryContactLastName: false,
    deliveryContactPhoneNumber: false,
    deliveryContactEmail: false,
    shippingDocsEmail: false,
  });

  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
  };

  const changeReceivingHrs = (dateIndex: any, isReceivingHrsFrom: any, dropdownValue: any) => {
    setValue(`dates.${dateIndex}.is_user_available`, true);
    const receivingHrsOption: any[] = [];
    let currentDropdown = `dates.${dateIndex}.to`;
    let adjacentDropdown = `dates.${dateIndex}.from`;
    let adjDropDownOptionsCopy = RecevingHoursFrom;
    let dropDownOptionsToBeDisabled = `dates.${dateIndex}.receivingHrsFrom`;
    let onChangingCancelAdjDropDownValue = RecevingHoursFrom[0].value;
    if (isReceivingHrsFrom) {
      currentDropdown = `dates.${dateIndex}.from`;
      adjacentDropdown = `dates.${dateIndex}.to`;
      adjDropDownOptionsCopy = RecevingHoursTo;
      onChangingCancelAdjDropDownValue = RecevingHoursTo[RecevingHoursTo.length - 2].value;
      dropDownOptionsToBeDisabled = `dates.${dateIndex}.receivingHrsTo`;
    }
    setValue(currentDropdown, dropdownValue.toString());
    if (dropdownValue === 'closed') {
      setValue(adjacentDropdown, dropdownValue.toString());
      setValue(`dates.${dateIndex}.is_user_available`, 0);
    }
    else if (watch(adjacentDropdown) === 'closed') {
      setValue(`dates.${dateIndex}.is_user_available`, 1);
      setValue(adjacentDropdown, onChangingCancelAdjDropDownValue.toString());
    }
    adjDropDownOptionsCopy.forEach(timeOption => {
      const time = { ...timeOption };
      if (dropdownValue !== 'closed' && ((!isReceivingHrsFrom && time.value >= dropdownValue) || (isReceivingHrsFrom && time.value <= dropdownValue))) time.disabled = true;
      receivingHrsOption.push(time);
    })
    setValue(dropDownOptionsToBeDisabled, receivingHrsOption);
  }

  const deleteResaleCertificateLine = (index) => {
    const resaleCertList = getValues("resaleCertificateList");
    if (resaleCertList?.length) {
      let list = resaleCertList.filter((x: any, i: any) => {
        if (i !== index) {
          return x
        }
      });
      if (list.length <= 0) {
        list = [...list, defaultResaleCertificateLine];
      }
      setValue("resaleCertificateList", list);
      trigger("resaleCertificateList");
    }
  }

  const openDeletePopup = (cert_id, index) => {
    setPayloadData({ cert_id, index });
    setOpenDeleteConfirmation(true)
  }


  const uploadCertFile = async (file, i) => {
    if (file) {
      setValue(`resaleCertificateList.${i}.uploadCertProgress`, true)
      // setValue(`resaleCertificateList.${i}.state_id`, '');
      // setValue(`resaleCertificateList.${i}.expiration_date`, '');
      // setValue(`resaleCertificateList.${i}.status`, null);
      setValue(`resaleCertificateList.${i}.file_name`, file.name);
      let index = file.name.length - 1;
      for (; index >= 0; index--) {
        if (file.name.charAt(index) === '.') {
          break;
        }
      }
      const ext = file.name.substring(index + 1, file.name.length);

      const objectKey = import.meta.env.VITE_ENVIRONMENT + '/' + userData.data.id + '/' + prefixUrl.resaleCertPrefix + '-' + uuidv4() + '.' + ext;

      const payload = {
        data: {
          "bucket_name": import.meta.env.VITE_S3_UPLOAD_SETTINGS_RESALE_CERT_BUCKET_NAME,
          "object_key": objectKey,
          "expire_time": 300

        }
      }
      let setCertUrl = 'https://' + payload.data.bucket_name + ".s3.amazonaws.com/" + payload.data.object_key;
      setValue(`resaleCertificateList.${i}.resaleCertFile`, '')
      axios.post(import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', payload)
        .then(response => {
          const signedUrl = response.data.data;
          axios.put(signedUrl, file)
            .then(async (response) => {
              if (response.status === 200) {
                setValue(`resaleCertificateList.${i}.uploadCertProgress`, false)
                setValue(`resaleCertificateList.${i}.cerificate_url_s3`, setCertUrl)
                await saveResaleCertificates(getValues("resaleCertificateList"))
                // queryClient.invalidateQueries([reactQueryKeys.getBuyingPreference])
                setValue(`resaleCertificateList.${i}.status`, 'Pending')
                setValue(`resaleCertificateList.${i}.state_code`, referenceData?.ref_states?.find((state: any) => state.id === watch(`resaleCertificateList.${i}.state_id`))?.code)
                showCommonDialog(commomKeys.uploadSuccessful, buyerSettingConst.uploadCertDialogContent, commomKeys.actionStatus.success, resetDialogStore, [{ name: commomKeys.successBtnTitle, action: resetDialogStore }])
              }
            })
            .catch(error => {
              console.error(error);
              setShowLoader(false);
              let uploadProgress = null;
              if (getValues(`resaleCertificateList.${i}.cerificate_url_s3`)) {
                uploadProgress = false;
              }
              setValue(`resaleCertificateList.${i}.uploadCertProgress`, uploadProgress)
              showCommonDialog(null, "Something went wrong. Please try again in sometime", commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
            }
            );
        })
        .catch(error => {
          console.error(error);
          // setisDataLoad(false);
          setShowLoader(false);
          let uploadProgress = null;
          if (getValues(`resaleCertificateList.${i}.cerificate_url_s3`)) {
            uploadProgress = false;
          }
          setValue(`resaleCertificateList.${i}.uploadCertProgress`, uploadProgress)
          showCommonDialog(null, "Something went wrong. Please try again in sometime", commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])

        }
        );

    }
  }

  const saveResaleCertificates = async (resaleCertificates: any) => {
    if (resaleCertificates?.length > 0) {
      let stateCodeText = "";

      let payload = [];
      resaleCertificates.forEach(certificate => {
        // const state = states.find(state => state.id === certificate.stateId);
        // if (state && (certificate.status === 'Pending' || !certificate.status)) {
        //   stateCodeText += state.code + ", ";
        // }

        if (certificate.cerificate_url_s3) {
          payload.push({
            cerificate_url_s3: certificate.cerificate_url_s3,
            expiration_date: certificate.expiration_date,
            file_name: certificate.file_name,
            id: certificate.id,
            state_id: certificate.state_id,
            status: certificate.status,
          })
        }
      });
      try {
        const response = saveUserSettings({ route: 'user/buyer/settings/shipment', data: {
            resale_certificate: payload
          }
        });
      } catch (err) {
        console.error(err)
        setShowLoader(false);
      }
    }
  }

  const resaleCertificateListing = watch("resaleCertificateList")?.map((resaleCertificate, index) => {
    return (
      <div key={index} className={styles.deleteCertLine}>

        <ResaleCertificateLineComponent
          index={index}
          resaleCertificate={resaleCertificate}
          deleteResaleCertificateLine={deleteResaleCertificateLine}
          deleteCertificateTooltip={deleteCertificateTooltip}
          openDeletePopup={openDeletePopup}
          register={register}
          uploadCertFile={uploadCertFile}
          States={States}
          ResaleExpiration={ResaleExpiration}
          control={control}
          errors={errors}
          setValue={setValue}
        />
      </div>
    )
  });

  const deleteCerti = () => {
    const payload = {
      data: {
        cert_id: payloadData.cert_id,
      },
    };

    setOpenDeleteConfirmation(false)
    axios.post(import.meta.env.VITE_API_SERVICE + `/user/deleteResaleCert`, payload)
      .then(response => {
        if (response.data.data.error_message) {
          showCommonDialog(null, response.data.data.error_message, commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
        }
        else {
          const certId = getValues(`resaleCertificateList.${payloadData.index}.id`)
          if (payloadData.cert_id === certId) {
            deleteResaleCertificateLine(payloadData.index);
          }
        }

      })
      .catch(error => {
        console.error('Error deleting file:', error);
      });
  };

  const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
    try {
      if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
        const payload = {
        data: {
          state_id: getValues(stateCode),
          zip_code: parseInt(getValues(zipCode)),
        },
      };
      const checkStateZipResponse = await axios.post(
        import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
        payload
      );
      if (checkStateZipResponse.data.data === true) {
        clearErrors([stateCode, zipCode]);
        return true
      } else {
        setError(stateCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
        setError(zipCode, { message: "The zip code and state code do not match" }, { shouldFocus: true });
        return false
        }
      }
    } catch (err) {
      console.error(err)
    }
  };

  const handleAddNewLine = () => {
    const resaleCertList = getValues("resaleCertificateList")
    setValue(`resaleCertificateList.${resaleCertList?.length}`, defaultResaleCertificateLine)
  }

  const saveReceivingHours = async () => {
    const dates = getValues("dates")
    try {
      const payload = {
          user_delivery_receiving_availability_details: dates.map((date: any) => {
            const { receivingHrsFrom, receivingHrsTo, ...rest } = date;
            return rest;
          })
      }
      saveUserSettings({ route: 'user/buyer/settings/shipment', data: payload });
    } catch (err) {
      console.error(err)
    }
  }

  const handleSaveShipmentSettings = async () => {
      const fieldsToValidate = [];
      const userSettingsPayload: any = {};
      try {
        if (watch('addressNickName') && watch('addressNickName') !== "") {
          fieldsToValidate.push('addressNickName');
          userSettingsPayload.address_nickname = watch('addressNickName');
        }
        userSettingsPayload.delivery_appt_required = watch('deliveryApptRequired');
        if (watch('deliveryContactName') && watch('deliveryContactName') !== "") {
          fieldsToValidate.push('deliveryContactName');
          userSettingsPayload.delivery_contact_name = watch('deliveryContactName');
        }

        if (watch('deliveryPhoneNumber') && watch('deliveryPhoneNumber') !== "") {
          const isValid = await trigger('deliveryPhoneNumber');
          if(isValid) {
            userSettingsPayload.delivery_phone = unformatPhoneNumber(watch('deliveryPhoneNumber'));
          }
        }

        if (watch('deliveryEmailAddress') && watch('deliveryEmailAddress') !== "") {
          const isValid = await trigger('deliveryEmailAddress');
          if(isValid) {
            userSettingsPayload.delivery_email_id = watch('deliveryEmailAddress');
          }
        }

        if(watch('shippingDocsEmail') && watch('shippingDocsEmail') !== "") {
          const isValid = await trigger('shippingDocsEmail');
          if(isValid) {
            userSettingsPayload.shipping_docs_to = watch('shippingDocsEmail');
          }
        }

        if (
          watch('deliveryAddress.line1') && watch('deliveryAddress.line1') !== "" &&
          watch('deliveryAddress.city') && watch('deliveryAddress.city') !== "" &&
          watch('deliveryAddress.state') && watch('deliveryAddress.state') !== "" &&
          watch('deliveryAddress.zip') && watch('deliveryAddress.zip') !== ""
        ) {
          // Check if there are existing errors for buyerAddress
          const hasExistingErrors = errors?.deliveryAddress?.line1 ||
            errors?.deliveryAddress?.line2 ||
            errors?.deliveryAddress?.city ||
            errors?.deliveryAddress?.state ||
            errors?.deliveryAddress?.zip;

          // Only add to validation if no existing errors (to preserve manual errors)
          if (!hasExistingErrors) {
            fieldsToValidate.push('deliveryAddress');
            // Add all fields to the payload
            userSettingsPayload.delivery_address = {
              line1: watch('deliveryAddress.line1'),
              line2: watch('deliveryAddress.line2')?.trim() || null,
              city: watch('deliveryAddress.city'),
              state_id: watch('deliveryAddress.state'),
              zip: watch('deliveryAddress.zip')
            }
          }
        }
        if (fieldsToValidate.length > 0) {
          const isValid = await trigger(fieldsToValidate);
          if (isValid && Object.keys(userSettingsPayload).length > 0) {
            saveUserSettings({ route: 'user/buyer/settings/shipment', data: userSettingsPayload });
            // Reset dirty state for successfully validated and saved fields
            fieldsToValidate.forEach((fieldName) => {
              const currentValue = watch(fieldName);
              resetField(fieldName, {
                  defaultValue: currentValue,
                  keepError: false,
                  keepDirty: false,
                  keepTouched: true
              });
          });
          }
        }
      } catch (err) {
        console.error(err)
      }
  }

  const handleSaveShipment = async (data: any) => {
    try {
      setShowLoader(true)
      if (isValid) {
        const payload = {
          address_nickname: data.locationNickName,
          delivery_address: {
            line1: data.locationAddress.line1,
            line2: data.locationAddress.line2 || null,
            city: data.locationAddress.city,
            state_id: Number(data.locationAddress.state),
            zip: data.locationAddress.zip,
          },
          delivery_appt_required: data.deliveryApptRequired,
          delivery_contact_first_name: data.deliveryContact.firstName,
          delivery_contact_last_name: data.deliveryContact.lastName,
          delivery_phone: data.deliveryContact.phone,
          delivery_email_id: data.deliveryContact.email,
          shipping_docs_to: data.shippingDocsEmail,
          user_delivery_receiving_availability_details: data.dates.map((date: any) => {
            const { receivingHrsFrom, receivingHrsTo, ...rest } = date;
            return rest;
          }),
          is_default: data.isDefault,
        }
        if(data.id &&  !isCreate){
          payload.id = data.id;
        }
        const res =await saveUserSettings({ route: 'user/buyer/settings/shipment', data: payload });
        if(res){
          queryClient.invalidateQueries()
          closeDialog()
        }
      }
    } catch (err) {
      console.error(err)
    } finally {
      setShowLoader(false)
    }
  }

  const handleDeleteShipment = async () => {
    console.log("handle delete")
  }


  return (<>
    <div className={styles.shipmentTabContentContainer}>
      <div className={styles.shipmentTabContentHeader}>
        <h2>{isCreate ? "CREATE NEW SHIP-TO" : "EDIT SHIP-TO"}</h2>
        <button onClick={closeDialog}>{isCreate ? "CANCEL" : "CANCEL EDITING"}<CloseIcon /></button>
      </div>
      <div className={styles.shipmentTabContentBody}>
        <div className={styles.shipmentTabContent}>
          <div className={styles.shipmentTabContentTitle}>
            Location Nickname
          </div>
          <div className={styles.shipmentTabContentValue}>
            <InputWrapper>
              <CustomTextField
                className={clsx(styles.inputCreateAccount, errors?.locationNickName && styles.error)}
                type='text'
                register={register("locationNickName")}
                placeholder='Example: Houston Yard'
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register("locationNickName").onBlur(e);
                  handleInputBlur('locationNickName')
                }}
                onFocus={() => handleInputFocus('locationNickName')}
                errorInput={errors?.locationNickName}
              />
            </InputWrapper>
          </div>
        </div>
        <div className={clsx(styles.shipmentTabContent, styles.companyHQAddressContainer)}>
          <div className={styles.shipmentTabContentTitle}>
            location address
          </div>
          <div className={styles.shipmentTabContentValue}>
            <div className={clsx(styles.customAddressContainer)}>
              <InputWrapper>
                <CustomTextField
                  className={clsx(styles.inputCreateAccount, errors?.locationAddress?.line1 && styles.error)}
                  type='text'
                  register={register('locationAddress.line1')}
                  placeholder='Line 1'
                  onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                    register('locationAddress.line1').onBlur(e);
                    handleInputBlur('locationAddressLine1')
                  }}
                  onFocus={() => handleInputFocus('locationAddressLine1')}
                  errorInput={errors?.locationAddress?.line1}
                />
              </InputWrapper>
              <InputWrapper>
                <CustomTextField
                  className={clsx(styles.inputCreateAccount, errors?.locationAddress?.line2 && styles.error)}
                  type='text'
                  // autoFocus={true}
                  register={register('locationAddress.line2')}
                  placeholder='Line 2'
                  onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                    register('locationAddress.line2').onBlur(e);
                    handleInputBlur('locationAddressLine2')
                  }}
                  onFocus={() => handleInputFocus('locationAddressLine2')}
                  errorInput={errors?.locationAddress?.line2}
                />
              </InputWrapper>

              <span className={styles.zipInputContainer}>
                <span className={styles.col1}>
                  <InputWrapper>
                    <CustomTextField
                      className={clsx(styles.inputCreateAccount, errors?.locationAddress?.city && styles.error)}
                      type='text'
                      register={register('locationAddress.city')}
                      placeholder='City'
                      onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                        register('locationAddress.city').onBlur(e);
                        handleInputBlur('locationAddressCity')
                      }}
                      onFocus={() => handleInputFocus('locationAddressCity')}
                      errorInput={errors?.locationAddress?.city}
                    />
                  </InputWrapper>
                </span>
                <span className={clsx(styles.inputSection, styles.yourLocationAdressState, styles.col2, styles.bdrRadius0, styles.bdrRight0)}>
                  {/* <CustomMenu
                      control={control}
                      name={"locationAddress.state"}
                      placeholder={'State'}
                      MenuProps={{
                        classes: {
                          paper: clsx(styles.Dropdownpaper, styles.Dropdownpaper1),
                          list: styles.muiMenuList,
                          select: styles.selectClassName,
                        },
                      }}
                      className={clsx(styles.selectDropdown, styles.selectState)}
                      items={States.map((x: any) => ({ title: x.code, value: x.id }))}
                      onChange={(e: any) => {
                        States.map((item: any) => {
                          if (item.id === e.target.value) {
                            setValue("locationAddress.stateCode", item.code)
                          }
                        })
                      }}
                    /> */}
                  <Controller
                    name="locationAddress.state"
                    control={control}
                    render={({ field }) => (
                      <>
                        <SingleStateSelector
                          states={States.map((state: any) => ({ state_code: state.code }))}
                          value={field.value}
                          onChange={(stateCode) => {
                            const selectedState = States.find((state: any) => state.code === stateCode);
                            field.onChange(selectedState?.id || '');
                            setValue('locationAddress.stateCode', selectedState?.code || '');
                          }}
                          onBlur={field.onBlur}
                          error={!!errors?.locationAddress?.state}
                          placeholder="State"
                          stateIdToCode={(stateId) => {
                            const state = States.find((s: any) => s.id === stateId);
                            return state ? state.code : stateId;
                          }}
                        />
                      </>
                    )}
                  />
                </span>
                <span className={styles.col3}>
                  <InputWrapper>
                    <CustomTextField
                      className={clsx(styles.inputCreateAccount, (errors?.locationAddress?.zip || errors?.locationAddress?.state) && styles.error)}
                      type='text'
                      maxLength={5}
                      register={register('locationAddress.zip')}
                      placeholder='Zip Code'
                      onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                        register('locationAddress.zip').onBlur(e);
                        handleInputBlur('locationAddressZip');
                      }}
                      onFocus={() => handleInputFocus('locationAddressZip')}
                      errorInput={errors?.locationAddress?.zip || errors?.locationAddress?.state}
                      mode="wholeNumber"
                    />
                  </InputWrapper>
                </span>
              </span>

            </div>
          </div>
        </div>
        <div className={clsx(styles.shipmentTabContent, styles.receivingHoursInput)}>
          <div className={styles.shipmentTabContentTitle} htmlFor="deliveryApptRequired">
            RECEIVING HOURS
          </div>
          <div className={styles.shipmentTabContentValue}>
            {watch('dates')?.map((x: any, i: any) => (<span key={x.day} className={styles.inputSectionRecevingHours}>
              <span className={`${watch(`dates.${i}.from`) !== 'closed' ? styles.daylbl1 : styles.daylbl1}`}>{x.display_name}</span>
              <span className={clsx(styles.daylbl2, 'w100 dflex')}>
                <CustomMenu
                  control={control}
                  defaultValue={x.from}
                  name={`dates.${i}.from`}
                  // className={'selectReceivingHours selectUploadCertDropdown'}
                  className={clsx((!dirtyFields.dates?.[i]?.from && 'disabledDropdown'), (x.from === 'closed' && 'txtClosed'), 'selectReceivingHours selectUploadCertDropdown')}
                  MenuProps={MenuPropsTop}
                  items={x.receivingHrsFrom}
                  IconComponent={DropdownIcon}
                  onChange={(events: any) => {
                    changeReceivingHrs(i, true, events.target.value);
                  }}
                />
              </span>
              <span className={clsx(styles.daylbl3, 'w100 dflex')}>
                <CustomMenu
                  defaultValue={x.to}
                  control={control}
                  name={`dates.${i}.to`}
                  className={clsx((!dirtyFields.dates?.[i]?.to && 'disabledDropdown'), (x.to === 'closed' && 'txtClosed'), 'selectReceivingHours selectUploadCertDropdown')}
                  MenuProps={MenuPropsBottom}
                  IconComponent={DropdownIcon}
                  items={x.receivingHrsTo}
                  onChange={(events: any) => {
                    changeReceivingHrs(i, false, events.target.value);
                  }}
                />
              </span>
            </span>))}
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={styles.shipmentTabContentTitle} htmlFor="deliveryApptRequired">
            DELIVERY APPT REQUIRED?
          </div>
          <div className={styles.shipmentTabContentValue}>
            <Select
              value={watch('deliveryApptRequired') || false}
              onChange={(event: any) => {
                setValue('deliveryApptRequired', event.target.value)
              }}
              className={clsx('selectDropdown', styles.dropdownValue)}
              MenuProps={
                {
                  classes: {
                    paper: styles.dropDownBG
                  },
                }
              }
            >
              <MenuItem value={true}>Yes</MenuItem>
              <MenuItem value={false}>No</MenuItem>
            </Select>
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={styles.shipmentTabContentTitle} htmlFor="deliveryContactFirstName">
            DELIVERY CONTACT NAME
          </div>
          <div className={styles.shipmentTabContentValue}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.deliveryContact?.firstName && styles.error
                )}
                id='deliveryContactFirstName'
                type='text'
                register={register('deliveryContact.firstName')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('deliveryContact.firstName').onBlur(e);
                  handleInputBlur('deliveryContactFirstName');
                }}
                onFocus={() => handleInputFocus('deliveryContactFirstName')}
                errorInput={errors?.deliveryContact?.firstName}
              // onKeyDown={(e) => {
              //   if(e.key === 'Tab'){
              //     if(e.shiftKey){
              //       setActiveTab('COMPANY');
              //     }
              //   }
              // }}
              />
            </InputWrapper>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.deliveryContact?.lastName && styles.error
                )}
                id='deliveryContactLastName'
                type='text'
                register={register('deliveryContact.lastName')}
                placeholder=''
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('deliveryContact.lastName').onBlur(e);
                  handleInputBlur('deliveryContactLastName');
                }}
                onFocus={() => handleInputFocus('deliveryContactLastName')}
                errorInput={errors?.deliveryContact?.lastName}
              // onKeyDown={(e) => {
              //   if(e.key === 'Tab'){
              //     if(e.shiftKey){
              //       setActiveTab('COMPANY');
              //     }
              //   }
              // }}
              />
            </InputWrapper>
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={styles.shipmentTabContentTitle} htmlFor="deliveryPhoneNumber">
            DELIVERY PHONE NUMBER
          </div>
          <div className={styles.shipmentTabContentValue}>
            <InputWrapper>
              <CustomTextField
                tabIndex={watch('deliveryApptRequired') ? 0 : -1}
                className={clsx(styles.inputCreateAccount, errors?.deliveryContact?.phone && styles.error)}
                type='tel'
                register={register("deliveryContact.phone")}
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register("deliveryContact.phone").onBlur(e);
                  handleInputBlur('deliveryContactPhoneNumber')
                }}
                onFocus={() => handleInputFocus('deliveryContactPhoneNumber')}
                errorInput={errors?.deliveryContact?.phone}
                mode="phoneNumber"
                placeholder='(xxx) xxx-xxxx'
              />
            </InputWrapper>
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={styles.shipmentTabContentTitle} htmlFor="deliveryEmailAddress">
            DELIVERY EMAIL ADDRESS
          </div>
          <div className={styles.shipmentTabContentValue}>
            <EmailTagInputField
              value={watch('deliveryContact.email') ? watch('deliveryContact.email').split(',').filter(Boolean) : []}
              onChange={(emails) => {
                setValue('deliveryContact.email', emails.join(','));
                // Trigger validation after setting the value
                trigger('deliveryContact.email');
              }}
              placeholder="Enter email addresses..."
              maxEmails={5}
              register={register("deliveryContact.email")}
              error={errors?.deliveryContact?.email}
              onBlur={() => trigger('deliveryContact.email')}
              control={control}
            />
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={styles.shipmentTabContentTitle}>
            Email shipping docs to
          </div>
          <div className={styles.shipmentTabContentValue}>
            <EmailTagInputField
              value={watch('shippingDocsEmail') ? watch('shippingDocsEmail').split(',').filter(Boolean) : []}
              onChange={(emails) => {
                setValue('shippingDocsEmail', emails.join(','));
                // Trigger validation after setting the value
                trigger('shippingDocsEmail');
              }}
              placeholder="Enter email addresses..."
              maxEmails={5}
              register={register("shippingDocsEmail")}
              error={errors?.deliveryContact?.email}
              onBlur={() => trigger('shippingDocsEmail')}
              control={control}
            />
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={styles.shipmentTabContentTitle} tmlFor="isDefault">
            Set as default
          </div>
          <div className={styles.shipmentTabContentValue}>
            <CustomToggleCheckbox
              name="isDefault"
              control={control}
              onChange={
                (e: any) => {
                  setValue('isDefault', e);
                }
              }
            />
          </div>
        </div>
        </div>
        <div className={styles.footerContainer}>
          {
            !isCreate && (
                <button className={styles.deleteBtn} onClick={() => setOpenDeleteConfirmation(true)}>Delete Ship-To</button>
            )
          }
          <button className={styles.saveBtn} onClick={() => handleSubmit(handleSaveShipment)()} disabled={isButtonDisabled} >Save</button>
        </div>
        <Dialog
          open={openDeleteConfirmation}
          transitionDuration={200}
          hideBackdrop
          classes={{
            root: styles.ErrorDialog,
            paper: styles.dialogContent
          }}
          container={shipmentPopupRef.current}
          style={{
            position: 'absolute',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          PaperProps={{
            style: {
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              margin: 0,
              backgroundColor: 'transparent'
            }
          }}
        >
          <div className={styles.deleteDialogContainer}>
            <p className={styles.deleteDialogTitle}>Are you sure you want to delete ?</p>
            <div className={styles.deleteBtnSection}>
              <button className={styles.submitYesBtn} onClick={handleDeleteShipment}>Yes</button>
              <button className={styles.submitNoBtn} onClick={() => setOpenDeleteConfirmation(false)}>No</button>
            </div>
          </div>
        </Dialog>

      </div>
    </>
    );
};

    export default ShipmentsTab;


